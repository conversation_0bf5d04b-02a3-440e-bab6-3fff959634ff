#!/usr/bin/env python3
"""
Test file for Deepview MCP
This is a simple Python script to test the Deepview functionality.
"""

def hello_world():
    """Simple hello world function"""
    print("Hello, World!")
    return "Hello from Deepview test!"

def calculate_sum(a, b):
    """Calculate the sum of two numbers"""
    return a + b

class TestClass:
    """A simple test class"""
    
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"

if __name__ == "__main__":
    hello_world()
    result = calculate_sum(5, 3)
    print(f"Sum: {result}")
    
    test_obj = TestClass("Deepview")
    print(test_obj.greet())
